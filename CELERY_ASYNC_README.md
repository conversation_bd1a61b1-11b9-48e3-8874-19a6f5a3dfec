# 协议解析异步化重构

## 🎯 项目概述

本次重构将原有的同步协议解析功能改造为基于 Celery+Redis 的异步处理架构，提升系统并发处理能力和用户体验。

## 🏗️ 核心架构

### 异步处理流程
```
客户端请求 → FastAPI → Celery Task → Redis Queue → Worker 处理 → 结果存储
```

### 技术栈
- **FastAPI**: Web API 框架
- **Celery**: 分布式任务队列
- **Redis**: 消息代理和结果存储
- **Circus**: 进程管理和监控

## 📁 项目结构

```
├── api/
│   └── protocol_api.py          # 重构后的协议解析API
├── celery_task/                 # Celery任务模块
│   ├── __init__.py
│   ├── celery.py               # Celery应用配置
│   ├── celeryconfig.py         # Redis连接配置
│   └── protocol_task.py        # 协议解析任务实现
├── config/                     # 配置管理
│   ├── settings.py             # 项目设置
│   └── nacos_config.py         # Nacos配置管理
├── test/                       # 测试文件
│   ├── test_protocol_async.py  # 异步功能测试
│   ├── test_docker_celery.py   # Docker环境测试
│   └── test_local_setup.py     # 本地环境测试
└── APP-META/docker-config/     # Docker部署配置
    ├── Dockerfile              # 更新支持Celery
    └── environment/common/app/
        ├── conf/circus.ini     # Circus进程配置
        └── bin/
            ├── appctl.sh       # 增强的管理脚本
            └── celery_realtime_monitor.sh  # Celery监控脚本
```

## 🚀 API 接口

### 异步接口
- `POST /protocols/extract-by-docx-async` - 提交异步解析任务
- `GET /protocols/task-status/{task_id}` - 查询任务状态

### 兼容接口
- `POST /protocols/extract-by-docx` - 同步解析（向后兼容）

## 🔧 本地开发

### 启动服务
```bash
# 使用Circus启动所有服务
circusd circus_local.ini

# 或者分别启动
uvicorn app:app --host 127.0.0.1 --port 7860
celery -A celery_task worker --loglevel=INFO --pool=threads --concurrency=4
```

### 运行测试
```bash
python test/test_protocol_async.py
python test/test_local_setup.py
```

## 🐳 Docker 部署

### 构建镜像
```bash
docker build -f APP-META/docker-config/Dockerfile -t yiya-ai-bot:protocol-async .
```

### 管理命令
```bash
# 查看服务状态
docker exec container_name /root/yiya-ai-bot/bin/appctl.sh status

# 检查Celery状态
docker exec container_name /root/yiya-ai-bot/bin/appctl.sh celery-status

# 重启Celery Worker
docker exec container_name /root/yiya-ai-bot/bin/appctl.sh celery-restart
```

## 📊 监控和调试

### 日志查看
- FastAPI: `logs/application.log`
- Celery Worker: `logs/celery.log`
- Celery错误: `logs/celery_error.log`

### Celery监控
```bash
# 查看Worker状态
celery -A celery_task inspect active

# 查看队列状态
celery -A celery_task inspect reserved

# 启动Flower监控界面
celery -A celery_task flower --port=5555
```

## 🔄 数据库隔离

- **Redis DB4**: 协议解析任务专用
- **队列名称**: `protocol_parse_queue`
- **避免与其他服务冲突**

## ✅ 主要特性

1. **异步处理**: 大文件解析不阻塞API响应
2. **任务跟踪**: 实时查询处理进度和结果
3. **错误处理**: 完善的异常捕获和重试机制
4. **向后兼容**: 保持原有同步API可用
5. **监控完善**: 进程监控和自动恢复
6. **部署就绪**: 完整的Docker和Circus配置

## 📝 注意事项

1. **文档位置**: 详细文档保存在本地 `docs/` 目录，不提交到git
2. **测试文件**: 统一放在 `test/` 目录下
3. **配置隔离**: 使用独立的Redis数据库避免冲突
4. **进程管理**: 生产环境使用Circus，开发环境可直接启动

## 🎉 升级收益

- **性能提升**: 异步处理提高并发能力
- **用户体验**: 即时响应，后台处理
- **系统稳定**: 完善的监控和恢复机制
- **运维友好**: 丰富的管理和调试工具
