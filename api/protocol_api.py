# api/protocol_api.py

from celery.result import AsyncResult
from fastapi import APIRouter

from celery_task.celery import celery_app
from celery_task.protocol_task import extract_protocol_task
from logger.logger import app_logger
from models.protocol_vo import ProtocolFileRequest
from models.result import make_fail, make_success

router = APIRouter(prefix="/protocols", tags=["Protocols"])


@router.post("/extract-by-docx-async")
async def extract_structure_from_docx_async(request: ProtocolFileRequest):
    """
    异步从OSS上的.docx方案文件中解析出层级化JSON结构。
    - 提交任务到 Celery 队列，立即返回任务 ID
    - 客户端可以通过任务 ID 查询处理状态和结果
    """
    file_key = request.protocol_file.file_key
    extract_keys_only = getattr(request, "extract_keys_only", False)

    try:
        app_logger.info(f"提交协议解析任务: {file_key}, extract_keys_only: {extract_keys_only}")

        # 提交任务到 Celery 队列
        task = extract_protocol_task.delay(file_key, extract_keys_only)
        task_id = task.id

        app_logger.info(f"协议解析任务已提交，Task ID: {task_id}")

        response_data = {
            "task_id": task_id,
            "file_key": file_key,
            "extract_keys_only": extract_keys_only,
            "message": "协议解析任务已成功提交，正在后台处理中。请使用 task_id 查询处理状态。",
        }

        return make_success(response_data, 0)

    except Exception as e:
        app_logger.error(f"提交协议解析任务失败, Key: '{file_key}'. 错误: {e}", exc_info=True)
        return make_fail(500, f"Failed to submit protocol parsing task: {e}")


@router.get("/task-status/{task_id}")
async def get_protocol_task_status(task_id: str):
    """
    查询协议解析任务的状态和结果
    """
    try:
        app_logger.info(f"查询协议解析任务状态: {task_id}")

        # 获取任务结果
        task_result = AsyncResult(task_id, app=celery_app)

        if task_result.state == "PENDING":
            # 任务还在等待执行
            response_data = {"task_id": task_id, "status": "PENDING", "message": "任务正在等待执行"}
        elif task_result.state == "PROGRESS":
            # 任务正在执行中
            response_data = {
                "task_id": task_id,
                "status": "PROGRESS",
                "progress_info": task_result.info,
                "message": "任务正在执行中",
            }
        elif task_result.state == "SUCCESS":
            # 任务执行成功
            result = task_result.result
            response_data = {"task_id": task_id, "status": "SUCCESS", "result": result, "message": "任务执行成功"}
        elif task_result.state == "FAILURE":
            # 任务执行失败
            response_data = {
                "task_id": task_id,
                "status": "FAILURE",
                "error": str(task_result.info),
                "message": "任务执行失败",
            }
        else:
            # 其他状态
            response_data = {
                "task_id": task_id,
                "status": task_result.state,
                "info": task_result.info,
                "message": f"任务状态: {task_result.state}",
            }

        return make_success(response_data, 0)

    except Exception as e:
        app_logger.error(f"查询任务状态失败, Task ID: '{task_id}'. 错误: {e}", exc_info=True)
        return make_fail(500, f"Failed to get task status: {e}")


# 保留原有的同步接口，用于向后兼容
@router.post("/extract-by-docx")
async def extract_structure_from_docx(request: ProtocolFileRequest):
    """
    从OSS上的.docx方案文件中解析出层级化JSON结构（同步版本，保持向后兼容）。
    推荐使用异步版本 /extract-by-docx-async
    """
    # 重定向到异步版本
    return await extract_structure_from_docx_async(request)
