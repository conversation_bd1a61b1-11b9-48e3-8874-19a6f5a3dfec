# 🚀 协议解析服务 API 调用方式重大更新

## 📋 概述

本文档说明了协议解析服务在 `feature/protocol-async-celery` 分支中的**重大接口变更**。主要将同步处理改为异步任务处理，大幅提升了服务性能和稳定性。

## ⚠️ **重要变更**

### 🔄 **接口架构调整**
- ❌ **废弃**: `/protocols/extract-by-docx` （同步接口，已重定向到异步版本）
- ✅ **新增**: `/protocols/extract-by-docx-async` （异步任务提交）
- ✅ **新增**: `/protocols/task-status/{task_id}` （任务状态查询）

### 🎯 **处理模式变更**
- **之前**: 同步处理，客户端需要等待完整处理时间
- **现在**: 异步处理，立即返回任务ID，客户端轮询获取结果

## 🌐 新的 API 调用方式

### 步骤1: 提交异步任务

#### 1.1 通过网关调用（推荐）

```bash
curl --location 'https://dev-gw.yiya-ai.com/protocols/extract-by-docx-async' \
--header 'x-fag-appcode: gateway-taya' \
--header 'x-fag-servicename: protocol' \
--header 'Authorization: Bearer yy-ieihaegp8fxckxics7bifr3d' \
--header 'Content-Type: application/json' \
--data '{
    "protocol_file": {
        "file_name": "382712.docx",
        "file_key": "rc-upload-1753675160704-21750661956754.docx"
    },
    "extract_keys_only": true
}'
```

#### 1.2 直接调用服务

```bash
curl --location 'http://dev.bot.yiya-ai.com/protocols/extract-by-docx-async' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer app-OOTWVAXPHLVJrzuLOace0A3C' \
--data '{
    "protocol_file": {
        "file_name": "protocol/123/临床研究方案.docx",
        "file_key": "protocol/123/临床研究方案.docx"
    },
    "extract_keys_only": true
}'
```

#### 1.3 本地开发调用

```bash
curl --location 'http://localhost:7860/protocols/extract-by-docx-async' \
--header 'Content-Type: application/json' \
--data '{
    "protocol_file": {
        "file_name": "protocol/123/临床研究方案.docx",
        "file_key": "protocol/123/临床研究方案.docx"
    },
    "extract_keys_only": false
}'
```

#### 📋 响应示例

```json
{
    "code": 0,
    "message": "success",
    "data": {
        "task_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
        "file_key": "protocol/123/临床研究方案.docx",
        "extract_keys_only": true,
        "message": "协议解析任务已成功提交，正在后台处理中。请使用 task_id 查询处理状态。"
    }
}
```

### 步骤2: 查询任务状态

使用步骤1返回的 `task_id` 查询任务状态：

#### 2.1 通过网关查询

```bash
curl --location 'https://dev-gw.yiya-ai.com/protocols/task-status/a1b2c3d4-e5f6-7890-abcd-ef1234567890' \
--header 'x-fag-appcode: gateway-taya' \
--header 'x-fag-servicename: protocol' \
--header 'Authorization: Bearer yy-ieihaegp8fxckxics7bifr3d'
```

#### 2.2 直接调用查询

```bash
curl --location 'http://dev.bot.yiya-ai.com/protocols/task-status/a1b2c3d4-e5f6-7890-abcd-ef1234567890' \
--header 'Authorization: Bearer app-OOTWVAXPHLVJrzuLOace0A3C'
```

#### 2.3 本地开发查询

```bash
curl --location 'http://localhost:7860/protocols/task-status/a1b2c3d4-e5f6-7890-abcd-ef1234567890'
```

## 📊 任务状态说明

### 任务状态类型

| 状态 | 说明 | 响应示例 |
|------|------|----------|
| `PENDING` | 任务等待执行 | `{"status": "PENDING", "message": "任务正在等待执行"}` |
| `PROGRESS` | 任务执行中 | `{"status": "PROGRESS", "progress_info": {...}, "message": "任务正在执行中"}` |
| `SUCCESS` | 任务执行成功 | `{"status": "SUCCESS", "result": {...}, "message": "任务执行成功"}` |
| `FAILURE` | 任务执行失败 | `{"status": "FAILURE", "error": "错误信息", "message": "任务执行失败"}` |

### 状态查询响应示例

#### ✅ 任务成功完成
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "task_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
        "status": "SUCCESS",
        "result": {
            "protocol_structure": {
                // 解析后的协议结构数据
            }
        },
        "message": "任务执行成功"
    }
}
```

#### ⏳ 任务执行中
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "task_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
        "status": "PROGRESS",
        "progress_info": {
            "current_step": "正在解析文档结构",
            "progress": 45
        },
        "message": "任务正在执行中"
    }
}
```

#### ❌ 任务执行失败
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "task_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
        "status": "FAILURE",
        "error": "文件不存在或无法访问",
        "message": "任务执行失败"
    }
}
```

## 📋 请求参数说明

### 核心参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `protocol_file` | Object | ✅ | 协议文件信息 |
| `protocol_file.file_name` | String | ✅ | 文件名称 |
| `protocol_file.file_key` | String | ✅ | 文件存储键值（OSS路径） |
| `extract_keys_only` | Boolean | ❌ | 是否仅提取关键信息，默认 `false` |

### 认证头说明

| 调用方式 | 认证头 | 说明 |
|----------|--------|------|
| 网关调用 | `Authorization: Bearer yy-ieihaegp8fxckxics7bifr3d` | 网关统一认证 |
| 直接调用 | `Authorization: Bearer app-OOTWVAXPHLVJrzuLOace0A3C` | 应用级认证 |
| 本地调用 | 无需认证头 | 开发环境免认证 |

## 🔄 完整调用示例

### Python 示例

```python
import requests
import time
import json

def extract_protocol_async(file_key, extract_keys_only=False):
    """异步协议解析完整示例"""

    # 步骤1: 提交任务
    submit_url = "http://localhost:7860/protocols/extract-by-docx-async"
    payload = {
        "protocol_file": {
            "file_name": "临床研究方案.docx",
            "file_key": file_key
        },
        "extract_keys_only": extract_keys_only
    }

    response = requests.post(submit_url, json=payload)
    if response.status_code != 200:
        print(f"任务提交失败: {response.text}")
        return None

    result = response.json()
    task_id = result["data"]["task_id"]
    print(f"任务已提交，Task ID: {task_id}")

    # 步骤2: 轮询任务状态
    status_url = f"http://localhost:7860/protocols/task-status/{task_id}"

    while True:
        response = requests.get(status_url)
        if response.status_code != 200:
            print(f"状态查询失败: {response.text}")
            return None

        result = response.json()
        status = result["data"]["status"]

        if status == "SUCCESS":
            print("任务执行成功！")
            return result["data"]["result"]
        elif status == "FAILURE":
            print(f"任务执行失败: {result['data']['error']}")
            return None
        elif status in ["PENDING", "PROGRESS"]:
            print(f"任务状态: {status}")
            time.sleep(2)  # 等待2秒后再次查询
        else:
            print(f"未知状态: {status}")
            time.sleep(2)

# 使用示例
result = extract_protocol_async("protocol/123/临床研究方案.docx", True)
if result:
    print("解析结果:", json.dumps(result, indent=2, ensure_ascii=False))
```

## 🔍 健康检查端点

新增了多个健康检查端点，便于监控和运维：

```bash
# 基础健康检查
curl http://localhost:7860/health

# 详细健康检查
curl http://localhost:7860/health/detailed

# 就绪检查（K8s readiness probe）
curl http://localhost:7860/readiness

# 存活检查（K8s liveness probe）
curl http://localhost:7860/liveness
```

## 🔄 从同步接口迁移指南

### 旧接口（已废弃）
```bash
# ❌ 旧的同步调用方式（不推荐）
curl --location 'http://localhost:7860/protocols/extract-by-docx' \
--header 'Content-Type: application/json' \
--data '{...}'
```

### 新接口（推荐）
```bash
# ✅ 新的异步调用方式
# 步骤1: 提交任务
curl --location 'http://localhost:7860/protocols/extract-by-docx-async' \
--header 'Content-Type: application/json' \
--data '{...}'

# 步骤2: 查询状态（使用返回的task_id）
curl --location 'http://localhost:7860/protocols/task-status/{task_id}'
```

### 迁移优势

| 对比项 | 同步接口 | 异步接口 |
|--------|----------|----------|
| **响应时间** | 需等待完整处理时间（可能几分钟） | 立即返回（毫秒级） |
| **超时风险** | 容易超时，特别是大文件 | 无超时风险 |
| **并发处理** | 阻塞式，影响其他请求 | 非阻塞，支持高并发 |
| **错误处理** | 处理失败需重新提交 | 可查询详细错误信息 |
| **进度跟踪** | 无法获取处理进度 | 支持进度查询 |

## 🚨 重要注意事项

### 1. **接口兼容性**
- ⚠️ `/protocols/extract-by-docx` 已重定向到异步版本，但**强烈建议**直接使用新接口
- 🔄 旧接口调用会自动转为异步处理，但客户端仍需轮询获取结果
- 📅 旧接口将在后续版本中完全移除

### 2. **轮询策略建议**
- ⏱️ **轮询间隔**: 建议 2-5 秒查询一次状态
- 🔄 **最大重试**: 建议设置最大查询次数（如 100 次）
- ⏰ **超时处理**: 建议设置总超时时间（如 10 分钟）

### 3. **错误处理**
- 📝 **任务失败**: 检查 `status` 为 `FAILURE` 时的 `error` 字段
- 🔍 **调试信息**: 查看应用日志获取详细错误信息
- 🔄 **重试机制**: 失败任务可以重新提交

### 4. **性能优化**
- 🚀 **批量处理**: 对于多个文件，可并行提交多个任务
- 💾 **结果缓存**: 相同文件的解析结果会被缓存
- 📊 **监控指标**: 通过健康检查端点监控服务状态

## 🔍 健康检查端点

新增了多个健康检查端点，便于监控和运维：

```bash
# 基础健康检查
curl http://localhost:7860/health

# 详细健康检查
curl http://localhost:7860/health/detailed

# 就绪检查（K8s readiness probe）
curl http://localhost:7860/readiness

# 存活检查（K8s liveness probe）
curl http://localhost:7860/liveness
```

## 🛠️ 技术改进说明

### 1. **架构优化**
- 🔄 引入 Celery 分布式任务队列
- 📊 Redis 作为消息代理和结果存储
- 🚀 支持水平扩展和负载均衡

### 2. **稳定性提升**
- 🛡️ 启动前预检查机制
- 🔧 优化 Gunicorn 配置管理
- 🔄 改进错误处理和重试机制

### 3. **监控增强**
- 📈 详细的任务状态跟踪
- 📝 完善的日志记录
- 🔍 多层级健康检查

