# 🚀 协议解析服务 API 调用方式更新说明

## 📋 概述

本文档说明了协议解析服务在 `feature/protocol-async-celery` 分支中的接口调用方式变更。主要优化了应用启动配置，提升了服务稳定性。

## 🔧 主要改动

### 1. 应用启动优化
- ✅ 统一了 Gunicorn 配置管理
- ✅ 增加了启动前预检查机制
- ✅ 优化了 Nacos 配置初始化流程
- ✅ 改进了 K8s 环境下的端口复用机制

### 2. 配置文件调整
- 新增 `gunicorn.py` 配置文件
- 优化 `circus.ini` 环境变量传递
- 增强健康检查端点

## 🌐 接口调用方式

### 1. 通过网关调用（推荐）

**环境**: 开发环境  
**地址**: `https://dev-gw.yiya-ai.com`

```bash
curl --location 'https://dev-gw.yiya-ai.com/protocols/extract-by-docx' \
--header 'x-fag-appcode: gateway-taya' \
--header 'x-fag-servicename: protocol' \
--header 'Authorization: Bearer yy-<PERSON><PERSON><PERSON><PERSON>p8fxckxics7bifr3d' \
--header 'Content-Type: application/json' \
--data '{
    "protocol_file": {
        "file_name": "382712.docx",
        "file_key": "rc-upload-1753675160704-21750661956754.docx"
    },
    "extract_keys_only": true
}'
```

**特点**:
- ✅ 统一网关管理
- ✅ 自动负载均衡
- ✅ 统一认证和鉴权
- ✅ 请求链路追踪

### 2. 直接调用服务（内部使用）

**环境**: 开发环境  
**地址**: `http://dev.bot.yiya-ai.com`

```bash
curl --location 'http://dev.bot.yiya-ai.com/protocols/extract-by-docx' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer app-OOTWVAXPHLVJrzuLOace0A3C' \
--data '{
    "protocol_file": {
        "file_name": "protocol/123/临床研究方案.docx",
        "file_key": "protocol/123/临床研究方案.docx"
    },
    "extract_keys_only": true
}'
```

**特点**:
- ⚡ 直接访问，延迟更低
- 🔧 适合内部服务调用
- 📊 便于调试和监控

### 3. 本地开发调用

**环境**: 本地开发  
**地址**: `http://localhost:7860`

```bash
curl --location 'http://localhost:7860/protocols/extract-by-docx' \
--header 'Content-Type: application/json' \
--data '{
    "protocol_file": {
        "file_name": "protocol/123/临床研究方案.docx",
        "file_key": "protocol/123/临床研究方案.docx"
    },
    "extract_keys_only": false
}'
```

**特点**:
- 🛠️ 本地开发测试
- 🚫 无需认证头
- 🔍 便于调试和开发

## 📊 请求参数说明

### 核心参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `protocol_file` | Object | ✅ | 协议文件信息 |
| `protocol_file.file_name` | String | ✅ | 文件名称 |
| `protocol_file.file_key` | String | ✅ | 文件存储键值 |
| `extract_keys_only` | Boolean | ❌ | 是否仅提取关键信息，默认 `false` |

### 认证头说明

| 调用方式 | 认证头 | 说明 |
|----------|--------|------|
| 网关调用 | `Authorization: Bearer yy-ieihaegp8fxckxics7bifr3d` | 网关统一认证 |
| 直接调用 | `Authorization: Bearer app-OOTWVAXPHLVJrzuLOace0A3C` | 应用级认证 |
| 本地调用 | 无需认证头 | 开发环境免认证 |

## 🔍 健康检查端点

新增了多个健康检查端点，便于监控和运维：

```bash
# 基础健康检查
curl http://localhost:7860/health

# 详细健康检查
curl http://localhost:7860/health/detailed

# 就绪检查（K8s readiness probe）
curl http://localhost:7860/readiness

# 存活检查（K8s liveness probe）
curl http://localhost:7860/liveness
```

## ⚠️ 重要变更说明

### 1. 启动流程优化
- 新增启动前预检查，确保 Redis、Nacos 等依赖服务正常
- 优化了 Nacos 配置加载流程，避免重复初始化
- 改进了 Gunicorn worker 启动机制，减少启动失败

### 2. 配置管理改进
- 统一使用 `gunicorn.py` 配置文件
- 优化了 K8s 环境变量传递机制
- 保留了 `--reuse-port` 配置，解决容器替换时的端口占用问题

### 3. 错误处理增强
- 增加了更详细的错误日志
- 优化了异常处理和重试机制
- 提供了更友好的错误信息

## 🚨 注意事项

1. **网关调用**：推荐使用网关方式，享受统一的认证、限流、监控等功能
2. **认证信息**：请确保使用正确的认证头，避免 401 错误
3. **文件路径**：`file_key` 需要是有效的 OSS 文件路径
4. **超时设置**：大文件处理可能需要较长时间，建议设置合适的超时时间

## 📞 联系方式

如有问题，请联系：
- 开发团队：[你的团队联系方式]
- 技术支持：[技术支持联系方式]

---

**更新时间**: 2025-07-29  
**版本**: feature/protocol-async-celery  
**维护者**: [你的名字]
