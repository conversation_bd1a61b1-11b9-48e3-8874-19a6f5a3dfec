# 多阶段构建：第一阶段用于打包应用
FROM alpine:latest as builder

# 安装tar工具
RUN apk add --no-cache tar

# 设置工作目录
WORKDIR /build

# 复制源代码
COPY . .

# 创建应用程序包
RUN tar -zcvf yiya-ai-bot.tgz \
      --exclude='APP-META' \
      --exclude='logs' \
      --exclude='.venv' \
      --exclude='model' \
      --exclude='nacos-data' \
      --exclude='.git' \
      --exclude='.idea' \
      --exclude='.env' \
      --exclude='__pycache__' \
      --exclude='*.pyc' \
      --exclude='*.log' \
      *

# 第二阶段：构建最终镜像
FROM yiya-acr-registry.cn-hangzhou.cr.aliyuncs.com/yiya/yiya-ai-bot-base:latest

ENV APP_NAME yiya-ai-bot
ENV APP_HOME /root

WORKDIR $APP_HOME/$APP_NAME

# 安装额外依赖（如果基础镜像中没有）
RUN apt update && apt-get install -y poppler-utils unrtf

# 从构建阶段复制应用程序包
COPY --from=builder /build/yiya-ai-bot.tgz $APP_HOME/$APP_NAME/target/

# 复制最新的配置文件，覆盖基础镜像中的旧配置
COPY APP-META/docker-config/environment/common/app/conf/circus.ini $APP_HOME/$APP_NAME/conf/circus.ini

# 复制修改后的appctl.sh，覆盖基础镜像中的旧版本
COPY APP-META/docker-config/environment/common/app/bin/appctl.sh $APP_HOME/$APP_NAME/bin/appctl.sh

# 复制 Celery 实时监控脚本
COPY APP-META/docker-config/environment/common/app/bin/celery_realtime_monitor.sh $APP_HOME/$APP_NAME/bin/celery_realtime_monitor.sh

# 设置执行权限
RUN chmod +x $APP_HOME/$APP_NAME/bin/appctl.sh && \
    chmod +x $APP_HOME/$APP_NAME/bin/celery_realtime_monitor.sh

# 启动应用
CMD ["/bin/bash", "-c", "/root/start.sh"]