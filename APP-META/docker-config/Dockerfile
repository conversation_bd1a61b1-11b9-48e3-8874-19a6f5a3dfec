FROM yiya-acr-registry.cn-hangzhou.cr.aliyuncs.com/yiya/yiya-ai-bot-base:latest

ENV APP_NAME yiya-ai-bot
ENV APP_HOME /root

WORKDIR $APP_HOME/$APP_NAME

# 安装额外依赖（poppler-utils 和 unrtf 在基础镜像中可能没有）
# 使用 --no-install-recommends 减少不必要的包，加速安装
RUN apt-get update && \
    DEBIAN_FRONTEND=noninteractive apt-get install -y --no-install-recommends \
        poppler-utils \
        unrtf && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# 复制应用程序包（由阿里云构建环境预先生成）
COPY $APP_NAME.tgz $APP_HOME/$APP_NAME/target/

# 复制最新的配置文件，覆盖基础镜像中的旧配置
COPY environment/common/app/conf/circus.ini $APP_HOME/$APP_NAME/conf/circus.ini

# 复制修改后的appctl.sh，覆盖基础镜像中的旧版本
COPY environment/common/app/bin/appctl.sh $APP_HOME/$APP_NAME/bin/appctl.sh

# 复制 Celery 实时监控脚本
COPY environment/common/app/bin/celery_realtime_monitor.sh $APP_HOME/$APP_NAME/bin/celery_realtime_monitor.sh

# 设置执行权限
RUN chmod +x $APP_HOME/$APP_NAME/bin/appctl.sh && \
    chmod +x $APP_HOME/$APP_NAME/bin/celery_realtime_monitor.sh

# 启动应用
CMD ["/bin/bash", "-c", "/root/start.sh"]