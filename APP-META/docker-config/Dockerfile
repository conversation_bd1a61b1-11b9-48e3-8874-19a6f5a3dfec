FROM yiya-acr-registry.cn-hangzhou.cr.aliyuncs.com/yiya/yiya-ai-bot-base:latest

ENV APP_NAME yiya-ai-bot
ENV APP_HOME /root

WORKDIR $APP_HOME/$APP_NAME

# 安装依赖
RUN apt update && apt-get install -y poppler-utils unrtf

COPY APP-META/docker-config/$APP_NAME.tgz $APP_HOME/$APP_NAME/target/

# 复制最新的配置文件，覆盖基础镜像中的旧配置
COPY APP-META/docker-config/environment/common/app/conf/circus.ini $APP_HOME/$APP_NAME/conf/circus.ini

# 复制修改后的appctl.sh，覆盖基础镜像中的旧版本
COPY APP-META/docker-config/environment/common/app/bin/appctl.sh $APP_HOME/$APP_NAME/bin/appctl.sh

# 复制 Celery 实时监控脚本
COPY APP-META/docker-config/environment/common/app/bin/celery_realtime_monitor.sh $APP_HOME/$APP_NAME/bin/celery_realtime_monitor.sh

# 设置执行权限
RUN chmod +x $APP_HOME/$APP_NAME/bin/appctl.sh && \
    chmod +x $APP_HOME/$APP_NAME/bin/celery_realtime_monitor.sh

# 启动应用
CMD ["/bin/bash", "-c", "/root/start.sh"]