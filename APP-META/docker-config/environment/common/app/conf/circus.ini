[circus]
check_delay=5
endpoint=tcp://127.0.0.1:5555
pubsub_endpoint=tcp://127.0.0.1:5556
statsd=true
pidfile=/root/logs/circus.pid


[watcher:{{app_name}}]
working_dir=/root/{{app_name}}/target/{{app_name}}
cmd=gunicorn app:app --workers 4 --timeout 180 --graceful-timeout 30 --max-requests 1000 --max-requests-jitter 100 --worker-class uvicorn.workers.UvicornWorker --worker-connections 1000 --bind 0.0.0.0:7860 --reuse-port
stop_signal=QUIT
# 启动配置
autostart=true
autorestart=true
restart_delay=10
# 增加启动超时时间，给 Nacos 初始化更多时间
warmup_delay=30
# 防止频繁重启
check_flapping=true
flapping_attempts=3
flapping_window=180
# 日志配置
stdout_stream.class=FileStream
stdout_stream.filename=/root/{{app_name}}/logs/application.log
stdout_stream.max_bytes=20971520
stdout_stream.backup_count=30
stderr_stream.class=FileStream
stderr_stream.filename=/root/{{app_name}}/logs/error.log
stderr_stream.max_bytes=20971520
stderr_stream.backup_count=30


[watcher:{{app_name}}-celery]
working_dir=/root/{{app_name}}/target/{{app_name}}
cmd=bash -c "celery -A celery_task worker --loglevel=INFO --pool=threads --concurrency=4 --logfile=- --time-limit=300 --soft-time-limit=240 --max-tasks-per-child=100 --prefetch-multiplier=1 2>&1 | tee /root/{{app_name}}/logs/celery.log"
numprocesses=1
stop_signal=QUIT

# 模仿gunicorn的健康检查配置
autostart=true
autorestart=true
restart_delay=10
max_age=3600
max_age_variance=300

# 进程监控配置
warmup_delay=30
check_flapping=true
flapping_attempts=3
flapping_window=180

# 错误处理
stderr_stream.class=FileStream
stderr_stream.filename=/root/{{app_name}}/logs/celery_error.log
stderr_stream.max_bytes=20971520
stderr_stream.backup_count=10

# 直接输出到标准输出，同时保存到文件
stdout_stream.class=StdoutStream


[watcher:{{app_name}}-celery-monitor]
working_dir=/root/{{app_name}}/target/{{app_name}}
cmd=/root/{{app_name}}/bin/celery_realtime_monitor.sh
numprocesses=1
autostart=true
autorestart=true
stop_signal=TERM
priority=20
stdout_stream.class=FileStream
stdout_stream.filename=/root/{{app_name}}/logs/celery_realtime_monitor.log
stdout_stream.max_bytes=5242880
stdout_stream.backup_count=3
stderr_stream.class=FileStream
stderr_stream.filename=/root/{{app_name}}/logs/celery_monitor_error.log
stderr_stream.max_bytes=5242880
stderr_stream.backup_count=3


[env]
PATH=$PATH
APP_LOG_DIR=$APP_LOG_DIR
APP_STAGE=$APP_STAGE
# K8s 环境变量
NACOS_SERVER=$NACOS_SERVER
NACOS_NAMESPACE=$NACOS_NAMESPACE
NACOS_TIMEOUT=$NACOS_TIMEOUT
NACOS_MAX_RETRIES=$NACOS_MAX_RETRIES
NACOS_RETRY_DELAY=$NACOS_RETRY_DELAY
NACOS_ENABLE_IN_CELERY=$NACOS_ENABLE_IN_CELERY
DISABLE_NACOS=$DISABLE_NACOS
# Redis 环境变量
REDIS_HOST=$REDIS_HOST
REDIS_PORT=$REDIS_PORT
REDIS_DB=$REDIS_DB
REDIS_PASSWORD=$REDIS_PASSWORD
REDIS_TASK_DB=$REDIS_TASK_DB
# 任务配置环境变量
TASK_RESULT_EXPIRE_TIME=$TASK_RESULT_EXPIRE_TIME
MAX_CONCURRENT_TASKS=$MAX_CONCURRENT_TASKS
TASK_TIMEOUT=$TASK_TIMEOUT
MAX_RETRY_TIMES=$MAX_RETRY_TIMES
