#!/usr/bin/env python3
"""
应用启动前的预检查脚本
用于验证关键依赖和配置是否正常
"""

import sys
import time
import logging
from typing import Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_redis_connection() -> Dict[str, Any]:
    """检查 Redis 连接"""
    try:
        from celery_task.celery import celery_app
        
        logger.info("检查 Redis 连接...")
        broker = celery_app.connection()
        broker.connect()
        broker.close()
        
        logger.info("✅ Redis 连接正常")
        return {"status": "ok", "message": "Redis connection successful"}
        
    except Exception as e:
        logger.error(f"❌ Redis 连接失败: {e}")
        return {"status": "error", "message": str(e)}


def check_nacos_connection() -> Dict[str, Any]:
    """检查 Nacos 连接"""
    try:
        logger.info("检查 Nacos 连接...")
        
        # 尝试初始化 Nacos，但不阻塞太久
        import signal
        
        def timeout_handler(signum, frame):
            raise TimeoutError("Nacos connection timeout")
        
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(10)  # 10秒超时
        
        try:
            from configurer.yy_nacos import Nacos, config
            Nacos()
            
            if config:
                logger.info(f"✅ Nacos 连接正常，已加载 {len(config)} 个配置")
                return {"status": "ok", "message": f"Nacos connected, {len(config)} configs loaded"}
            else:
                logger.warning("⚠️ Nacos 连接成功但未加载配置")
                return {"status": "warning", "message": "Nacos connected but no configs loaded"}
                
        finally:
            signal.alarm(0)  # 取消超时
            
    except TimeoutError:
        logger.warning("⚠️ Nacos 连接超时，将在应用启动时重试")
        return {"status": "warning", "message": "Nacos connection timeout, will retry during app startup"}
        
    except Exception as e:
        logger.warning(f"⚠️ Nacos 连接失败: {e}，将在应用启动时重试")
        return {"status": "warning", "message": f"Nacos connection failed: {str(e)}"}


def check_file_permissions() -> Dict[str, Any]:
    """检查文件权限"""
    try:
        import os
        
        logger.info("检查文件权限...")
        
        # 检查日志目录
        log_dir = "/root/yiya-ai-bot/logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
            
        if not os.access(log_dir, os.W_OK):
            raise PermissionError(f"Log directory {log_dir} is not writable")
            
        # 检查上传目录
        upload_dir = "/root/yiya-ai-bot/uploads"
        if not os.path.exists(upload_dir):
            os.makedirs(upload_dir, exist_ok=True)
            
        if not os.access(upload_dir, os.W_OK):
            raise PermissionError(f"Upload directory {upload_dir} is not writable")
            
        logger.info("✅ 文件权限检查通过")
        return {"status": "ok", "message": "File permissions OK"}
        
    except Exception as e:
        logger.error(f"❌ 文件权限检查失败: {e}")
        return {"status": "error", "message": str(e)}


def check_python_dependencies() -> Dict[str, Any]:
    """检查关键 Python 依赖"""
    try:
        logger.info("检查 Python 依赖...")
        
        required_modules = [
            'fastapi',
            'uvicorn',
            'celery',
            'redis',
            'nacos',
            'pydantic_settings'
        ]
        
        missing_modules = []
        for module in required_modules:
            try:
                __import__(module)
            except ImportError:
                missing_modules.append(module)
                
        if missing_modules:
            raise ImportError(f"Missing required modules: {', '.join(missing_modules)}")
            
        logger.info("✅ Python 依赖检查通过")
        return {"status": "ok", "message": "All required Python modules available"}
        
    except Exception as e:
        logger.error(f"❌ Python 依赖检查失败: {e}")
        return {"status": "error", "message": str(e)}


def main():
    """主检查函数"""
    logger.info("开始应用启动前检查...")
    
    checks = [
        ("Python 依赖", check_python_dependencies),
        ("文件权限", check_file_permissions),
        ("Redis 连接", check_redis_connection),
        ("Nacos 连接", check_nacos_connection),
    ]
    
    results = {}
    has_error = False
    
    for check_name, check_func in checks:
        logger.info(f"执行检查: {check_name}")
        result = check_func()
        results[check_name] = result
        
        if result["status"] == "error":
            has_error = True
            
    # 输出检查结果摘要
    logger.info("=" * 50)
    logger.info("检查结果摘要:")
    
    for check_name, result in results.items():
        status_icon = "✅" if result["status"] == "ok" else "⚠️" if result["status"] == "warning" else "❌"
        logger.info(f"{status_icon} {check_name}: {result['message']}")
        
    if has_error:
        logger.error("存在严重错误，应用可能无法正常启动")
        sys.exit(1)
    else:
        logger.info("✅ 所有检查通过，应用可以启动")
        sys.exit(0)


if __name__ == "__main__":
    main()
