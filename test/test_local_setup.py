#!/usr/bin/env python3
"""
本地环境测试脚本
验证 FastAPI + Celery + Redis 是否正常工作
"""

import requests
import time
import json
from pathlib import Path

def test_fastapi_health():
    """测试 FastAPI 健康检查"""
    try:
        response = requests.get("http://127.0.0.1:7860/health", timeout=5)
        if response.status_code == 200:
            print("✅ FastAPI 应用运行正常")
            return True
        else:
            print(f"❌ FastAPI 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ FastAPI 连接失败: {e}")
        return False

def test_redis_connection():
    """测试 Redis 连接"""
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=4, decode_responses=True)
        r.ping()
        print("✅ Redis 连接正常")
        return True
    except Exception as e:
        print(f"❌ Redis 连接失败: {e}")
        return False

def test_celery_worker():
    """测试 Celery Worker 是否运行"""
    try:
        from celery_task.celery import celery_app
        
        # 检查活跃的 worker
        inspect = celery_app.control.inspect()
        active_workers = inspect.active()
        
        if active_workers:
            print(f"✅ Celery Worker 运行正常: {list(active_workers.keys())}")
            return True
        else:
            print("❌ 没有发现活跃的 Celery Worker")
            return False
    except Exception as e:
        print(f"❌ Celery Worker 检查失败: {e}")
        return False

def test_async_protocol_api():
    """测试异步协议解析 API"""
    try:
        # 准备测试数据
        test_data = {
            "file_key": "test-protocol.docx",
            "file_name": "test-protocol.docx", 
            "extract_keys_only": True
        }
        
        # 提交异步任务
        response = requests.post(
            "http://127.0.0.1:7860/extract-by-docx-async",
            json=test_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 0 and "task_id" in result.get("data", {}):
                task_id = result["data"]["task_id"]
                print(f"✅ 异步任务提交成功: {task_id}")
                
                # 查询任务状态
                time.sleep(1)
                status_response = requests.get(
                    f"http://127.0.0.1:7860/task-status/{task_id}",
                    timeout=5
                )
                
                if status_response.status_code == 200:
                    status_result = status_response.json()
                    print(f"✅ 任务状态查询成功: {status_result.get('data', {}).get('state', 'UNKNOWN')}")
                    return True
                else:
                    print(f"❌ 任务状态查询失败: {status_response.status_code}")
                    return False
            else:
                print(f"❌ 异步任务提交失败: {result}")
                return False
        else:
            print(f"❌ 异步 API 调用失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 异步协议解析测试失败: {e}")
        return False

def test_sync_protocol_api():
    """测试同步协议解析 API（向后兼容）"""
    try:
        # 准备测试数据
        test_data = {
            "file_key": "test-protocol.docx",
            "file_name": "test-protocol.docx",
            "extract_keys_only": True
        }
        
        # 调用同步 API
        response = requests.post(
            "http://127.0.0.1:7860/extract-by-docx",
            json=test_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 0:
                print("✅ 同步协议解析 API 正常")
                return True
            else:
                print(f"❌ 同步 API 返回错误: {result}")
                return False
        else:
            print(f"❌ 同步 API 调用失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 同步协议解析测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 本地环境测试开始")
    print("=" * 50)
    
    tests = [
        ("FastAPI 健康检查", test_fastapi_health),
        ("Redis 连接测试", test_redis_connection), 
        ("Celery Worker 检查", test_celery_worker),
        ("异步协议解析 API", test_async_protocol_api),
        ("同步协议解析 API", test_sync_protocol_api),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        try:
            if test_func():
                passed += 1
            else:
                print(f"   测试失败")
        except Exception as e:
            print(f"   测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！本地环境配置正确")
        print("\n📋 下一步:")
        print("- 访问 http://127.0.0.1:7860/docs 查看 API 文档")
        print("- 使用 circusctl status 查看进程状态")
        print("- 查看 logs/ 目录下的日志文件")
        return True
    else:
        print("❌ 部分测试失败，请检查配置")
        print("\n🔧 故障排除:")
        print("- 确保 Redis 正在运行: redis-cli ping")
        print("- 检查 Circus 进程: circusctl status")
        print("- 查看日志: tail -f logs/*.log")
        return False

if __name__ == "__main__":
    main()
