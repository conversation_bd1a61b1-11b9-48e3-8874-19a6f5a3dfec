#!/usr/bin/env python3
# test_docker_celery.py

"""
Docker 环境下的 Celery 配置测试脚本
用于验证 Celery Worker、Redis 连接和任务执行是否正常
"""

import logging
import sys
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


def test_celery_import():
    """测试 Celery 模块导入"""
    try:
        from celery_task.celery import celery_app
        from celery_task.protocol_task import extract_protocol_task

        logger.info("✅ Celery 模块导入成功")
        return True, celery_app
    except ImportError as e:
        logger.error(f"❌ Celery 模块导入失败: {e}")
        return False, None
    except Exception as e:
        logger.error(f"❌ Celery 模块导入异常: {e}")
        return False, None


def test_redis_connection(celery_app):
    """测试 Redis 连接"""
    try:
        # 测试 broker 连接
        broker = celery_app.connection()
        broker.connect()
        broker.close()
        logger.info("✅ Redis broker 连接正常")

        # 测试 result backend 连接
        backend = celery_app.backend
        backend.get("test-key")  # 这会测试连接
        logger.info("✅ Redis result backend 连接正常")

        return True
    except Exception as e:
        logger.error(f"❌ Redis 连接失败: {e}")
        return False


def test_worker_registration(celery_app):
    """测试 Worker 注册状态"""
    try:
        inspect = celery_app.control.inspect()
        workers = inspect.registered()

        if workers:
            logger.info(f"✅ 发现 {len(workers)} 个注册的 Worker:")
            for worker, tasks in workers.items():
                logger.info(f"  - Worker: {worker}")
                logger.info(f"    任务: {tasks}")
            return True
        else:
            logger.warning("⚠️ 未发现注册的 Worker")
            return False
    except Exception as e:
        logger.error(f"❌ Worker 注册检查失败: {e}")
        return False


def test_queue_status(celery_app):
    """测试队列状态"""
    try:
        inspect = celery_app.control.inspect()

        # 检查活跃任务
        active = inspect.active()
        if active:
            total_active = sum(len(tasks) for tasks in active.values())
            logger.info(f"ℹ️ 活跃任务数: {total_active}")
        else:
            logger.info("ℹ️ 无活跃任务")

        # 检查队列中的任务
        reserved = inspect.reserved()
        if reserved:
            total_reserved = sum(len(tasks) for tasks in reserved.values())
            logger.info(f"ℹ️ 队列中任务数: {total_reserved}")
        else:
            logger.info("ℹ️ 队列为空")

        return True
    except Exception as e:
        logger.error(f"❌ 队列状态检查失败: {e}")
        return False


def test_task_submission(celery_app):
    """测试任务提交（不执行真实任务）"""
    try:
        from celery_task.protocol_task import extract_protocol_task

        # 提交一个测试任务（使用不存在的文件，预期会失败但能测试提交机制）
        task = extract_protocol_task.delay("test/non-existent-file.docx", True)
        task_id = task.id

        logger.info(f"✅ 任务提交成功，Task ID: {task_id}")

        # 等待一小段时间检查任务状态
        time.sleep(2)

        # 检查任务状态
        result = task.result
        state = task.state

        logger.info(f"ℹ️ 任务状态: {state}")

        if state in ["PENDING", "STARTED", "FAILURE"]:
            logger.info("✅ 任务状态正常（预期会失败因为文件不存在）")
            return True
        else:
            logger.warning(f"⚠️ 任务状态异常: {state}")
            return False

    except Exception as e:
        logger.error(f"❌ 任务提交测试失败: {e}")
        return False


def test_configuration():
    """测试配置"""
    try:
        from config.settings import settings

        logger.info(f"✅ Redis 配置: {settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DB}")

        from celery_task.celeryconfig import broker_url, result_backend

        logger.info(f"✅ Broker URL: {broker_url}")
        logger.info(f"✅ Result Backend: {result_backend}")

        return True
    except Exception as e:
        logger.error(f"❌ 配置检查失败: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("=" * 60)
    logger.info("🧪 Docker 环境 Celery 配置测试")
    logger.info("=" * 60)

    tests = [
        ("配置检查", test_configuration),
        ("Celery 模块导入", test_celery_import),
    ]

    results = {}
    celery_app = None

    # 执行基础测试
    for test_name, test_func in tests:
        logger.info(f"\n🔍 执行测试: {test_name}")
        if test_name == "Celery 模块导入":
            success, celery_app = test_func()
        else:
            success = test_func()
        results[test_name] = success

        if not success and test_name == "Celery 模块导入":
            logger.error("❌ 基础模块导入失败，跳过后续测试")
            break

    # 如果基础测试通过，执行 Celery 相关测试
    if celery_app:
        celery_tests = [
            ("Redis 连接", lambda: test_redis_connection(celery_app)),
            ("Worker 注册", lambda: test_worker_registration(celery_app)),
            ("队列状态", lambda: test_queue_status(celery_app)),
            ("任务提交", lambda: test_task_submission(celery_app)),
        ]

        for test_name, test_func in celery_tests:
            logger.info(f"\n🔍 执行测试: {test_name}")
            success = test_func()
            results[test_name] = success

    # 输出测试结果
    logger.info("\n" + "=" * 60)
    logger.info("📊 测试结果汇总:")
    logger.info("=" * 60)

    passed = 0
    total = len(results)

    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if success:
            passed += 1

    logger.info(f"\n总计: {passed}/{total} 项测试通过")

    if passed == total:
        logger.info("🎉 所有测试通过！Celery 配置正常")
        return 0
    else:
        logger.error("❌ 部分测试失败，请检查配置")
        return 1


if __name__ == "__main__":
    sys.exit(main())
