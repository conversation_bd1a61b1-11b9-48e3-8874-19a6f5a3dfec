#!/usr/bin/env python3
# test_protocol_async.py

import json
import sys
import time

import requests

# API 基础 URL
BASE_URL = "http://localhost:8000"


def test_async_protocol_parsing():
    """测试异步协议解析功能"""

    print("🚀 开始测试异步协议解析功能...")

    # 测试数据 - 使用一个示例文件 key（这个测试主要验证API接口，不依赖真实文件）
    test_file_key = "protocols/test-protocol.docx"  # 这是一个测试用的 key

    # 1. 提交异步任务
    print("\n📤 步骤 1: 提交协议解析任务")
    print(f"文件 Key: {test_file_key}")

    submit_url = f"{BASE_URL}/protocols/extract-by-docx-async"
    payload = {
        "protocol_file": {"file_name": "test-protocol.docx", "file_key": test_file_key},
        "extract_keys_only": False,
    }

    try:
        response = requests.post(submit_url, json=payload)
        print(f"HTTP 状态码: {response.status_code}")
        print(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")

        if response.status_code != 200:
            print("❌ 任务提交失败")
            return False

        result = response.json()
        if not result.get("success"):
            print("❌ 任务提交失败")
            return False

        task_id = result["result"]["task_id"]
        print(f"✅ 任务提交成功，Task ID: {task_id}")

    except Exception as e:
        print(f"❌ 任务提交异常: {e}")
        return False

    # 2. 轮询任务状态
    print("\n🔄 步骤 2: 查询任务状态")
    status_url = f"{BASE_URL}/protocols/task-status/{task_id}"

    max_attempts = 30  # 最多查询30次
    attempt = 0

    while attempt < max_attempts:
        attempt += 1
        print(f"\n查询第 {attempt} 次...")

        try:
            response = requests.get(status_url)
            print(f"HTTP 状态码: {response.status_code}")

            if response.status_code != 200:
                print("❌ 状态查询失败")
                break

            result = response.json()
            if not result.get("success"):
                print("❌ 状态查询失败")
                break

            status_data = result["result"]
            status = status_data["status"]
            print(f"任务状态: {status}")

            if status == "PENDING":
                print("⏳ 任务等待中...")
            elif status == "PROGRESS":
                progress_info = status_data.get("progress_info", {})
                current_stage = progress_info.get("current_stage", "未知")
                progress = progress_info.get("progress", 0)
                print(f"🔄 任务执行中: {current_stage} ({progress}%)")
            elif status == "SUCCESS":
                print("✅ 任务执行成功！")
                task_result = status_data["result"]
                print("结果摘要:")
                print(f"  - OSS Key: {task_result.get('oss_key', 'N/A')}")
                print(f"  - 总耗时: {task_result.get('total_time_seconds', 'N/A')} 秒")
                print(f"  - 关键信息条目数: {len(task_result.get('key_information', {}))}")

                # 显示性能报告
                perf_report = task_result.get("performance_report", {})
                if perf_report:
                    stage_times = perf_report.get("stage_times", {})
                    print("  - 性能统计:")
                    for stage, time_cost in stage_times.items():
                        print(f"    * {stage}: {time_cost}s")

                return True
            elif status == "FAILURE":
                print("❌ 任务执行失败")
                error = status_data.get("error", "未知错误")
                print(f"错误信息: {error}")
                return False
            else:
                print(f"⚠️  未知状态: {status}")

        except Exception as e:
            print(f"❌ 状态查询异常: {e}")
            break

        # 等待一段时间再查询
        if attempt < max_attempts:
            time.sleep(2)

    print("❌ 任务查询超时或失败")
    return False


def test_sync_compatibility():
    """测试同步接口的向后兼容性"""

    print("\n🔄 测试同步接口向后兼容性...")

    test_file_key = "protocols/test-protocol.docx"

    sync_url = f"{BASE_URL}/protocols/extract-by-docx"
    payload = {
        "protocol_file": {"file_name": "test-protocol.docx", "file_key": test_file_key},
        "extract_keys_only": False,
    }

    try:
        response = requests.post(sync_url, json=payload)
        print(f"HTTP 状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                task_id = result["result"]["task_id"]
                print(f"✅ 同步接口成功重定向到异步模式，Task ID: {task_id}")
                return True

        print("❌ 同步接口测试失败")
        return False

    except Exception as e:
        print(f"❌ 同步接口测试异常: {e}")
        return False


if __name__ == "__main__":
    print("=" * 60)
    print("🧪 协议解析异步功能测试")
    print("=" * 60)

    # 检查服务器是否运行
    try:
        health_response = requests.get(f"{BASE_URL}/health_check")
        if health_response.status_code != 200:
            print("❌ 服务器未运行或健康检查失败")
            sys.exit(1)
        print("✅ 服务器运行正常")
    except:
        print("❌ 无法连接到服务器，请确保服务器在 http://localhost:8000 运行")
        sys.exit(1)

    # 运行测试
    success = True

    # 测试异步功能
    if not test_async_protocol_parsing():
        success = False

    # 测试向后兼容性
    if not test_sync_compatibility():
        success = False

    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试通过！")
    else:
        print("❌ 部分测试失败")
    print("=" * 60)
