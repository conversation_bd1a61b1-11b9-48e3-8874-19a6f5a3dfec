import re, json


# text = """部分活化凝血酶原时间（APTT）
# 丙氨酸氨基转移酶（ALT）
# 天冬氨酸氨基转移酶 (AST)
# 碱性磷酸酶（ALP）
# 谷氨酰转肽酶（GGT）"""
#
# # 使用正则表达式匹配中文术语和缩略语
# pattern = r'(.+)\（([A-Z]+)）'
#
# matches = re.findall(pattern, text)
#
# # 将匹配结果转换为二元组列表
# result = [(match[1], match[0]) for match in matches]
#
# print(result)


def main(json_arr: list) -> dict:
    # 提取所有条目
    all_entries = []
    for json_str in json_arr:
        # 解析JSON字符串
        entry_list = json.loads(json_str)
        if entry_list:
            all_entries.extend(entry_list)

    # 去重
    unique_entries = {entry['zh']: entry for entry in all_entries}.values()

    # 按英文缩略语字母顺序排序
    sorted_entries = sorted(unique_entries, key=lambda x: x['en'])

    # 转换为JSON字符串
    result_json = json.dumps(sorted_entries, ensure_ascii=False)

    print(result_json)

    return {
        "result": result_json,
    }


import json


def format_md(arg1: str) -> dict:
    # 生成Markdown表格
    markdown_table = "| 英文缩略语 | 中文术语 |\n| --- | --- |\n"

    json_array = json.loads(arg1)

    for item in json_array:
        markdown_table += f"| {item['zh']} | {item['en']} |\n"

    return {
        "result": markdown_table,
    }


if __name__ == '__main__':
    # d = {
    #     "json_arr": [
    #         "[{\"zh\": \"丙氨酸氨基转移酶\", \"en\": \"ALT\"}, {\"zh\": \"碱性磷酸酶\", \"en\": \"ALP\"}, {\"zh\": \"谷氨酰转肽酶\", \"en\": \"GGT\"}, {\"zh\": \"血清总蛋白\", \"en\": \"TP\"}, {\"zh\": \"血清白蛋白\", \"en\": \"ALB\"}, {\"zh\": \"血清尿素氮\", \"en\": \"BUN\"}, {\"zh\": \"尿酸\", \"en\": \"UA\"}, {\"zh\": \"空腹血糖\", \"en\": \"GLU\"}, {\"zh\": \"甘油三酯\", \"en\": \"TG\"}, {\"zh\": \"总胆固醇\", \"en\": \"TC\"}, {\"zh\": \"高密度脂蛋白\", \"en\": \"HDL\"}, {\"zh\": \"低密度脂蛋白\", \"en\": \"LDL\"}, {\"zh\": \"血清氨\", \"en\": \"NH\"}, {\"zh\": \"凝血酶原时间\", \"en\": \"PT\"}, {\"zh\": \"国际标准化比值\", \"en\": \"INR\"}, {\"zh\": \"部分活化凝血酶原时间\", \"en\": \"APTT\"}, {\"zh\": \"凝血酶时间\", \"en\": \"TT\"}, {\"zh\": \"纤维蛋白原\", \"en\": \"FIB\"}, {\"zh\": \"梅毒螺旋体特异抗体\", \"en\": \"TPHA\"}, {\"zh\": \"急性淋巴细胞白血病\", \"en\": \"ALL\"}, {\"zh\": \"最大耐受剂量\", \"en\": \"MTD\"}, {\"zh\": \"世界卫生组织\", \"en\": \"WHO\"}, {\"zh\": \"ECOG全身状态\", \"en\": \"PS\"}]",
    #         "[]",
    #         "[{\"zh\": \"ECOG评分\", \"en\": \"ECOG\"}]",
    #         "[{\"zh\": \"部分活化凝血酶原时间\", \"en\": \"APTT\"}, {\"zh\": \"碱性磷酸酶\", \"en\": \"ALP\"}, {\"zh\": \"谷氨酰转肽酶\", \"en\": \"GGT\"}, {\"zh\": \"血清总蛋白\", \"en\": \"TP\"}, {\"zh\": \"血清白蛋白\", \"en\": \"ALB\"}, {\"zh\": \"血清尿素氮\", \"en\": \"BUN\"}, {\"zh\": \"尿酸\", \"en\": \"UA\"}, {\"zh\": \"空腹血糖\", \"en\": \"GLU\"}, {\"zh\": \"甘油三酯\", \"en\": \"TG\"}, {\"zh\": \"总胆固醇\", \"en\": \"TC\"}, {\"zh\": \"高密度脂蛋白\", \"en\": \"HDL\"}, {\"zh\": \"低密度脂蛋白\", \"en\": \"LDL\"}, {\"zh\": \"血清氨\", \"en\": \"NH\"}, {\"zh\": \"凝血酶原时间\", \"en\": \"PT\"}, {\"zh\": \"国际标准化比值\", \"en\": \"INR\"}, {\"zh\": \"凝血酶时间\", \"en\": \"TT\"}, {\"zh\": \"纤维蛋白原\", \"en\": \"FIB\"}]"
    #     ]
    # }
    # main(d['json_arr'])
    d = {
        "arg1": "[{\"zh\": \"血清白蛋白\", \"en\": \"ALB\"}, {\"zh\": \"碱性磷酸酶\", \"en\": \"ALP\"}, {\"zh\": \"丙氨酸氨基转移酶\", \"en\": \"ALT\"}, {\"zh\": \"部分活化凝血酶原时间\", \"en\": \"APTT\"}, {\"zh\": \"血清尿素氮\", \"en\": \"BUN\"}, {\"zh\": \"ECOG评分\", \"en\": \"ECOG\"}, {\"zh\": \"纤维蛋白原\", \"en\": \"FIB\"}, {\"zh\": \"谷氨酰转肽酶\", \"en\": \"GGT\"}, {\"zh\": \"空腹血糖\", \"en\": \"GLU\"}, {\"zh\": \"高密度脂蛋白\", \"en\": \"HDL\"}, {\"zh\": \"国际标准化比值\", \"en\": \"INR\"}, {\"zh\": \"低密度脂蛋白\", \"en\": \"LDL\"}, {\"zh\": \"血清氨\", \"en\": \"NH\"}, {\"zh\": \"凝血酶原时间\", \"en\": \"PT\"}, {\"zh\": \"总胆固醇\", \"en\": \"TC\"}, {\"zh\": \"甘油三酯\", \"en\": \"TG\"}, {\"zh\": \"血清总蛋白\", \"en\": \"TP\"}, {\"zh\": \"梅毒螺旋体特异抗体\", \"en\": \"TPHA\"}, {\"zh\": \"凝血酶时间\", \"en\": \"TT\"}, {\"zh\": \"尿酸\", \"en\": \"UA\"}]"
    }
    print(format_md(d['arg1']))
