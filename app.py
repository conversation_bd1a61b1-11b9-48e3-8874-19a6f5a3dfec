#!/usr/bin/env python
# encoding: utf-8
import asyncio
import time

from fastapi import APIRouter, FastAPI
from fastapi.middleware.cors import CORSMiddleware

from aop.context import ContextMiddleware
from api.doc_diff_api import router as doc_diff_router
from api.file_api import router as file_router
from api.file_loader import router as file_loader_router
from api.health import router as health_router
from api.img2md import router as img2md_router
from api.ocr_api import router as ocr_router
from api.pdf_parser import router as parser_router
from api.protocol_api import router as protocol_router
from api.tfl_api import router as tfl_router
from configurer.yy_nacos import Nacos
from logger.logger import app_logger


async def startup_event():
    """应用启动事件处理器，增加重试机制"""
    max_retries = 3
    retry_delay = 2

    for attempt in range(max_retries):
        try:
            app_logger.info(f"开始初始化 Nacos 配置 (尝试 {attempt + 1}/{max_retries})")
            start_time = time.time()

            # 使用异步方式初始化 Nacos，避免阻塞 worker 启动
            await asyncio.get_event_loop().run_in_executor(None, Nacos)

            elapsed_time = time.time() - start_time
            app_logger.info(f"Nacos 配置初始化成功，耗时 {elapsed_time:.2f} 秒")
            return

        except Exception as e:
            app_logger.error(f"Nacos 配置初始化失败 (尝试 {attempt + 1}/{max_retries}): {e}")

            if attempt < max_retries - 1:
                app_logger.info(f"等待 {retry_delay} 秒后重试...")
                await asyncio.sleep(retry_delay)
            else:
                app_logger.warning("Nacos 配置初始化最终失败，应用将在没有 Nacos 配置的情况下启动")
                app_logger.warning("某些依赖 Nacos 配置的功能可能不可用")


def create_app():
    _app = FastAPI()

    origins = [
        "*",
    ]

    _app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    _app.add_middleware(ContextMiddleware)

    # API routers
    router = APIRouter()
    router.include_router(health_router, include_in_schema=False)
    router.include_router(parser_router, include_in_schema=False)
    router.include_router(ocr_router, include_in_schema=False)
    router.include_router(file_router, include_in_schema=False)
    router.include_router(doc_diff_router, include_in_schema=False)
    router.include_router(file_loader_router, include_in_schema=False)
    router.include_router(img2md_router, include_in_schema=False)
    router.include_router(tfl_router, include_in_schema=False)
    router.include_router(protocol_router, include_in_schema=False)

    _app.include_router(router)

    # 使用异步启动事件处理器
    _app.add_event_handler("startup", startup_event)

    app_logger.info("app created.")
    return _app


app = create_app()

if __name__ == "__main__":
    import uvicorn

    uvicorn.run("app:app", host="127.0.0.1", port=7860, reload=True, access_log=False)
